@import "tailwindcss";

:root {
  /* Color Palette */
  --gradient-start: #1a0b2e;  /* Deep purple */
  --gradient-end: #16213e;    /* Navy */
  --background: #0a0a0f;      /* Deep space */
  --primary: #8b5cf6;         /* Vibrant purple */
  --text-primary: #ffffff;    /* Pure white */
  --text-muted: #a1a1aa;      /* Muted gray */

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-blur: 16px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(to bottom right, var(--gradient-start), var(--background), var(--gradient-end));
  background-attachment: fixed;
  color: var(--text-primary);
  overflow-x: hidden;
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
  }
}

@keyframes textStroke {
  0% {
    -webkit-text-stroke-color: #8b5cf6;
  }
  33% {
    -webkit-text-stroke-color: #a855f7;
  }
  66% {
    -webkit-text-stroke-color: #c084fc;
  }
  100% {
    -webkit-text-stroke-color: #8b5cf6;
  }
}

/* Utility animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-text-stroke {
  animation: textStroke 3s ease-in-out infinite;
}