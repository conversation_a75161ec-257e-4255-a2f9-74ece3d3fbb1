import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Github, TrendingUp, ShoppingCart, BarChart3, Bot, Code, Smartphone, Monitor } from 'lucide-react';

function Projects() {
  const [hoveredProject, setHoveredProject] = useState(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const projects = [
    {
      id: 'stock-predictor',
      title: 'ML Stock Predictor',
      description: 'Advanced machine learning model for stock price prediction using LSTM neural networks and real-time market data analysis.',
      size: 'large', // 2x2
      icon: TrendingUp,
      gradient: 'from-green-500 via-emerald-500 to-teal-600',
      technologies: ['Python', 'TensorFlow', 'Pandas', 'React', 'Chart.js'],
      features: ['Real-time predictions', 'Interactive charts', 'Historical analysis', 'Risk assessment'],
      github: '#',
      demo: '#',
      status: 'Live',
      animation: 'chart'
    },
    {
      id: 'ecommerce',
      title: 'E-Commerce Platform',
      description: 'Full-stack e-commerce solution with modern UI, secure payments, and admin dashboard.',
      size: 'medium', // 1x2
      icon: ShoppingCart,
      gradient: 'from-blue-500 via-indigo-500 to-purple-600',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Tailwind'],
      features: ['Payment integration', 'Admin panel', 'Inventory management', 'User authentication'],
      github: '#',
      demo: '#',
      status: 'In Development',
      animation: 'devices'
    },
    {
      id: 'data-visualizer',
      title: 'Data Visualizer',
      description: 'Interactive data visualization tool for complex datasets with multiple chart types and export options.',
      size: 'medium', // 1x2
      icon: BarChart3,
      gradient: 'from-purple-500 via-pink-500 to-rose-600',
      technologies: ['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL'],
      features: ['Multiple chart types', 'Real-time updates', 'Data export', 'Custom filters'],
      github: '#',
      demo: '#',
      status: 'Live',
      animation: 'bars'
    },
    {
      id: 'discord-bot',
      title: 'Discord Bot',
      description: 'Multi-purpose Discord bot with moderation, music, and utility commands.',
      size: 'small', // 1x1
      icon: Bot,
      gradient: 'from-indigo-500 via-blue-500 to-cyan-600',
      technologies: ['Python', 'Discord.py', 'SQLite'],
      features: ['Moderation tools', 'Music player', 'Custom commands'],
      github: '#',
      demo: '#',
      status: 'Live',
      stats: { servers: '50+', users: '1000+' }
    },
    {
      id: 'portfolio',
      title: 'Portfolio Website',
      description: 'This responsive portfolio website built with modern web technologies.',
      size: 'wide', // 2x1
      icon: Code,
      gradient: 'from-orange-500 via-red-500 to-pink-600',
      technologies: ['React', 'Tailwind CSS', 'Framer Motion', 'Vite'],
      features: ['Responsive design', 'Smooth animations', 'Dark theme', 'SEO optimized'],
      github: '#',
      demo: '#',
      status: 'Live',
      animation: 'code'
    }
  ];

  const getGridClasses = (size) => {
    switch (size) {
      case 'large': return 'col-span-2 row-span-2';
      case 'medium': return 'col-span-1 row-span-2';
      case 'wide': return 'col-span-2 row-span-1';
      case 'small': return 'col-span-1 row-span-1';
      default: return 'col-span-1 row-span-1';
    }
  };

  const AnimatedChart = () => (
    <div className="absolute bottom-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity">
      <svg width="60" height="40" viewBox="0 0 60 40">
        {[10, 25, 15, 30, 20, 35].map((height, index) => (
          <motion.rect
            key={index}
            x={index * 10}
            y={40 - height}
            width="8"
            height={height}
            fill="currentColor"
            initial={{ height: 0, y: 40 }}
            animate={{ height, y: 40 - height }}
            transition={{ duration: 1, delay: index * 0.1, repeat: Infinity, repeatDelay: 2 }}
          />
        ))}
      </svg>
    </div>
  );

  const AnimatedBars = () => (
    <div className="absolute bottom-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity">
      <div className="flex items-end space-x-1">
        {[20, 35, 15, 40, 25].map((height, index) => (
          <motion.div
            key={index}
            className="w-2 bg-current rounded-t"
            style={{ height: `${height}px` }}
            initial={{ height: 0 }}
            animate={{ height: `${height}px` }}
            transition={{ duration: 0.8, delay: index * 0.1, repeat: Infinity, repeatDelay: 3 }}
          />
        ))}
      </div>
    </div>
  );

  const AnimatedDevices = () => (
    <div className="absolute bottom-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity">
      <div className="flex items-end space-x-2">
        <Monitor className="w-6 h-6" />
        <Smartphone className="w-4 h-4" />
      </div>
    </div>
  );

  const AnimatedCode = () => (
    <div className="absolute bottom-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity">
      <motion.div
        animate={{ opacity: [0.2, 0.6, 0.2] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Code className="w-8 h-8" />
      </motion.div>
    </div>
  );

  const getAnimation = (type) => {
    switch (type) {
      case 'chart': return <AnimatedChart />;
      case 'bars': return <AnimatedBars />;
      case 'devices': return <AnimatedDevices />;
      case 'code': return <AnimatedCode />;
      default: return null;
    }
  };

  return (
    <section id="projects" className="min-h-screen py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--gradient-start)] via-[var(--background)] to-[var(--gradient-end)]">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10"></div>
      </div>

      <div className="relative z-10 container mx-auto px-8 lg:px-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={cardVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Featured <span className="text-primary">Projects</span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              A showcase of my recent work and technical achievements
            </p>
          </motion.div>

          {/* Projects Bento Grid */}
          <div className="grid grid-cols-3 grid-rows-3 gap-6 max-w-6xl mx-auto">
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                variants={cardVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                onHoverStart={() => setHoveredProject(project.id)}
                onHoverEnd={() => setHoveredProject(null)}
                className={`${getGridClasses(project.size)} glass-strong rounded-2xl p-6 relative overflow-hidden group cursor-pointer`}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${project.gradient} opacity-10 group-hover:opacity-20 transition-opacity duration-300`}></div>
                
                {/* Status Badge */}
                <div className="absolute top-4 right-4 z-20">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    project.status === 'Live' 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {project.status}
                  </span>
                </div>

                {/* Animated Elements */}
                {project.animation && getAnimation(project.animation)}

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${project.gradient} rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform`}>
                      <project.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-white font-bold text-lg">{project.title}</h3>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-white/80 text-sm mb-4 flex-1">{project.description}</p>

                  {/* Technologies */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.slice(0, project.size === 'large' ? 5 : 3).map((tech) => (
                        <span key={tech} className="px-2 py-1 bg-white/10 text-white/80 rounded text-xs">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features (for large cards) */}
                  {project.size === 'large' && (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-2">
                        {project.features.map((feature) => (
                          <div key={feature} className="flex items-center text-white/70 text-xs">
                            <div className={`w-1.5 h-1.5 bg-gradient-to-r ${project.gradient} rounded-full mr-2`}></div>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Stats (for small cards) */}
                  {project.stats && (
                    <div className="mb-4 grid grid-cols-2 gap-2">
                      <div className="text-center">
                        <div className="text-white font-bold">{project.stats.servers}</div>
                        <div className="text-white/60 text-xs">Servers</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white font-bold">{project.stats.users}</div>
                        <div className="text-white/60 text-xs">Users</div>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-auto">
                    <motion.a
                      href={project.github}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 flex items-center justify-center gap-2 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white text-sm transition-colors"
                    >
                      <Github className="w-4 h-4" />
                      Code
                    </motion.a>
                    <motion.a
                      href={project.demo}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`flex-1 flex items-center justify-center gap-2 py-2 bg-gradient-to-r ${project.gradient} rounded-lg text-white text-sm transition-all`}
                    >
                      <ExternalLink className="w-4 h-4" />
                      Demo
                    </motion.a>
                  </div>
                </div>

                {/* Hover Effect */}
                {hoveredProject === project.id && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl pointer-events-none"
                  />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}

export default Projects;
