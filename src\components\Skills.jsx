import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Code, Server, Brain, Trophy, Zap, Database, Globe, Smartphone } from 'lucide-react';

function Skills() {
  const [hoveredCard, setHoveredCard] = useState(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const skills = [
    {
      id: 'frontend',
      title: 'Frontend Development',
      icon: Code,
      size: 'large', // 2x2
      gradient: 'from-blue-500 via-cyan-500 to-teal-500',
      technologies: [
        { name: 'React', level: 90, icon: '⚛️' },
        { name: 'JavaScript', level: 85, icon: '🟨' },
        { name: 'TypeScript', level: 75, icon: '🔷' },
        { name: 'Tailwind CSS', level: 90, icon: '🎨' },
        { name: 'Next.js', level: 80, icon: '▲' },
        { name: 'Vue.js', level: 70, icon: '💚' }
      ],
      achievements: ['50+ Projects Built', 'Responsive Design Expert', 'Performance Optimization']
    },
    {
      id: 'backend',
      title: 'Backend Development',
      icon: Server,
      size: 'medium', // 1x2
      gradient: 'from-green-500 via-emerald-500 to-teal-600',
      technologies: [
        { name: 'Node.js', level: 85, icon: '🟢' },
        { name: 'Express.js', level: 80, icon: '🚀' },
        { name: 'MongoDB', level: 75, icon: '🍃' },
        { name: 'PostgreSQL', level: 70, icon: '🐘' }
      ],
      achievements: ['RESTful APIs', 'Database Design', 'Authentication Systems']
    },
    {
      id: 'ml',
      title: 'Machine Learning',
      icon: Brain,
      size: 'medium', // 1x2
      gradient: 'from-purple-500 via-violet-500 to-indigo-600',
      technologies: [
        { name: 'Python', level: 85, icon: '🐍' },
        { name: 'TensorFlow', level: 70, icon: '🧠' },
        { name: 'Scikit-learn', level: 75, icon: '📊' },
        { name: 'Pandas', level: 80, icon: '🐼' }
      ],
      achievements: ['Predictive Models', 'Data Analysis', 'Neural Networks']
    },
    {
      id: 'competitive',
      title: 'DSA & Competitive Programming',
      icon: Trophy,
      size: 'small', // 1x1
      gradient: 'from-orange-500 via-red-500 to-pink-600',
      technologies: [
        { name: 'C++', level: 80, icon: '⚡' },
        { name: 'Algorithms', level: 75, icon: '🔄' },
        { name: 'Data Structures', level: 85, icon: '📚' }
      ],
      achievements: ['500+ Problems Solved', 'Contest Participant']
    },
    {
      id: 'tools',
      title: 'Development Tools',
      icon: Zap,
      size: 'wide', // 2x1
      gradient: 'from-yellow-500 via-orange-500 to-red-500',
      technologies: [
        { name: 'Git', level: 85, icon: '📝' },
        { name: 'Docker', level: 70, icon: '🐳' },
        { name: 'VS Code', level: 90, icon: '💻' },
        { name: 'Figma', level: 75, icon: '🎨' }
      ],
      achievements: ['Version Control', 'Containerization', 'Design Systems']
    },
    {
      id: 'mobile',
      title: 'Mobile Development',
      icon: Smartphone,
      size: 'small', // 1x1
      gradient: 'from-indigo-500 via-purple-500 to-pink-500',
      technologies: [
        { name: 'React Native', level: 70, icon: '📱' },
        { name: 'Flutter', level: 65, icon: '🦋' }
      ],
      achievements: ['Cross-platform Apps', 'Native Performance']
    }
  ];

  const getGridClasses = (size) => {
    switch (size) {
      case 'large': return 'col-span-2 row-span-2';
      case 'medium': return 'col-span-1 row-span-2';
      case 'wide': return 'col-span-2 row-span-1';
      case 'small': return 'col-span-1 row-span-1';
      default: return 'col-span-1 row-span-1';
    }
  };

  return (
    <section id="skills" className="min-h-screen py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--gradient-start)] via-[var(--background)] to-[var(--gradient-end)]">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10"></div>
      </div>

      <div className="relative z-10 container mx-auto px-8 lg:px-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={cardVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Skills & <span className="text-primary">Expertise</span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              A comprehensive overview of my technical skills and proficiency levels
            </p>
          </motion.div>

          {/* Bento Grid */}
          <div className="grid grid-cols-3 grid-rows-3 gap-6 max-w-6xl mx-auto">
            {skills.map((skill, index) => (
              <motion.div
                key={skill.id}
                variants={cardVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                onHoverStart={() => setHoveredCard(skill.id)}
                onHoverEnd={() => setHoveredCard(null)}
                className={`${getGridClasses(skill.size)} glass-strong rounded-2xl p-6 relative overflow-hidden group cursor-pointer`}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${skill.gradient} opacity-10 group-hover:opacity-20 transition-opacity duration-300`}></div>
                
                {/* Floating Elements */}
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity">
                  <skill.icon className="w-8 h-8 text-white" />
                </div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${skill.gradient} rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform`}>
                      <skill.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-white font-bold text-lg">{skill.title}</h3>
                  </div>

                  {/* Technologies */}
                  <div className="flex-1 mb-4">
                    <div className="space-y-3">
                      {skill.technologies.slice(0, skill.size === 'large' ? 6 : skill.size === 'medium' ? 4 : 3).map((tech) => (
                        <div key={tech.name} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{tech.icon}</span>
                            <span className="text-white/90 text-sm">{tech.name}</span>
                          </div>
                          <div className="flex-1 mx-3">
                            <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                              <motion.div
                                initial={{ width: 0 }}
                                whileInView={{ width: `${tech.level}%` }}
                                transition={{ duration: 1, delay: index * 0.1 }}
                                className={`h-full bg-gradient-to-r ${skill.gradient} rounded-full`}
                              />
                            </div>
                          </div>
                          <span className="text-white/60 text-xs">{tech.level}%</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Achievements */}
                  {skill.size === 'large' && (
                    <div className="mt-auto">
                      <h4 className="text-white/80 font-semibold mb-2 text-sm">Key Achievements:</h4>
                      <div className="space-y-1">
                        {skill.achievements.map((achievement) => (
                          <div key={achievement} className="flex items-center text-white/60 text-xs">
                            <div className={`w-1.5 h-1.5 bg-gradient-to-r ${skill.gradient} rounded-full mr-2`}></div>
                            {achievement}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Hover Effect */}
                  {hoveredCard === skill.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl pointer-events-none"
                    />
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}

export default Skills;
