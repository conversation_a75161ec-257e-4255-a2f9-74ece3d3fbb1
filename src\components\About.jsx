import React from 'react';
import { motion } from 'framer-motion';
import { Code, Database, Brain, Gamepad2, Music, BookOpen, Award, Calendar } from 'lucide-react';

function About() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const interests = [
    {
      icon: Code,
      title: "Web Development",
      description: "Building responsive and interactive web applications",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Database,
      title: "Backend Systems",
      description: "Designing scalable server architectures and APIs",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Brain,
      title: "Machine Learning",
      description: "Exploring AI and data science applications",
      gradient: "from-purple-500 to-violet-500"
    },
    {
      icon: Gamepad2,
      title: "Gaming",
      description: "Passionate about game development and design",
      gradient: "from-orange-500 to-red-500"
    },
    {
      icon: Music,
      title: "Music Production",
      description: "Creating and mixing electronic music",
      gradient: "from-pink-500 to-rose-500"
    },
    {
      icon: BookOpen,
      title: "Continuous Learning",
      description: "Always exploring new technologies and concepts",
      gradient: "from-indigo-500 to-blue-500"
    }
  ];

  const education = [
    {
      year: "2020",
      title: "Secondary School",
      institution: "Local High School",
      description: "Completed with focus on Science and Mathematics",
      badges: ["HTML", "CSS", "Basic Programming"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      year: "2022",
      title: "Class 12 (Science)",
      institution: "Senior Secondary School",
      description: "Specialized in Physics, Chemistry, Mathematics, and Computer Science",
      badges: ["Physics", "Chemistry", "Mathematics", "Computer Science"],
      color: "from-green-500 to-emerald-500"
    },
    {
      year: "2022-2026",
      title: "B.Tech Computer Science",
      institution: "Engineering College",
      description: "Currently pursuing Bachelor's in Computer Science and Engineering",
      badges: ["Data Structures", "Algorithms", "Web Development", "Machine Learning", "Database Systems"],
      color: "from-purple-500 to-violet-500",
      features: [
        "Advanced Programming Concepts",
        "Software Engineering Principles",
        "AI and Machine Learning",
        "Full-Stack Development"
      ]
    }
  ];

  return (
    <section id="about" className="min-h-screen py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--gradient-start)] via-[var(--background)] to-[var(--gradient-end)]">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10"></div>
      </div>

      <div className="relative z-10 container mx-auto px-8 lg:px-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              About <span className="text-primary">Me</span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              Passionate developer with a love for creating innovative solutions and learning new technologies
            </p>
          </motion.div>

          {/* Main About Content */}
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Profile Card */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="glass-strong rounded-2xl p-8 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/20 to-purple-400/20 rounded-full blur-3xl"></div>
                <div className="relative z-10">
                  <div className="w-32 h-32 bg-gradient-to-br from-primary to-purple-600 rounded-full mx-auto mb-6 flex items-center justify-center text-4xl font-bold text-white">
                    H
                  </div>
                  <h3 className="text-2xl font-bold text-white text-center mb-4">Harshith</h3>
                  <p className="text-white/80 leading-relaxed">
                    I'm a passionate Computer Science student with a strong foundation in full-stack development 
                    and machine learning. I love turning complex problems into simple, beautiful solutions through code.
                  </p>
                  <div className="mt-6 flex flex-wrap gap-2">
                    {["React", "Node.js", "Python", "Machine Learning", "MongoDB"].map((tech) => (
                      <span key={tech} className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Interests Grid */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-2xl font-bold text-white mb-6">Interests & Passions</h3>
              <div className="grid grid-cols-2 gap-4">
                {interests.map((interest, index) => (
                  <motion.div
                    key={interest.title}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="glass rounded-xl p-4 group cursor-pointer"
                  >
                    <div className={`w-12 h-12 bg-gradient-to-r ${interest.gradient} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}>
                      <interest.icon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-white font-semibold mb-2">{interest.title}</h4>
                    <p className="text-white/60 text-sm">{interest.description}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Education Timeline */}
          <motion.div variants={itemVariants}>
            <h3 className="text-3xl font-bold text-white text-center mb-12">
              Education <span className="text-primary">Journey</span>
            </h3>
            
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-purple-400 to-primary"></div>
              
              <div className="space-y-12">
                {education.map((item, index) => (
                  <motion.div
                    key={item.year}
                    variants={itemVariants}
                    className="relative flex items-start"
                  >
                    {/* Timeline Dot */}
                    <div className={`absolute left-6 w-4 h-4 bg-gradient-to-r ${item.color} rounded-full border-4 border-background z-10`}></div>
                    
                    {/* Content */}
                    <div className="ml-16 glass-strong rounded-xl p-6 flex-1">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                        <div>
                          <span className={`inline-block px-3 py-1 bg-gradient-to-r ${item.color} text-white rounded-full text-sm font-semibold mb-2`}>
                            {item.year}
                          </span>
                          <h4 className="text-xl font-bold text-white">{item.title}</h4>
                          <p className="text-primary font-medium">{item.institution}</p>
                        </div>
                      </div>
                      
                      <p className="text-white/80 mb-4">{item.description}</p>
                      
                      {/* Badges */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {item.badges.map((badge) => (
                          <span key={badge} className="px-2 py-1 bg-white/10 text-white/80 rounded text-xs">
                            {badge}
                          </span>
                        ))}
                      </div>
                      
                      {/* Features (for current education) */}
                      {item.features && (
                        <div className="grid grid-cols-2 gap-2">
                          {item.features.map((feature) => (
                            <div key={feature} className="flex items-center text-white/70 text-sm">
                              <Award className="w-4 h-4 text-primary mr-2" />
                              {feature}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

export default About;
